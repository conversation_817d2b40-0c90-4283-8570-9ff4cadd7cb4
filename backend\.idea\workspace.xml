<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="a44b5110-00e2-47e2-9ec9-61df77808236" name="Changes" comment="">
      <change beforePath="$PROJECT_DIR$/src/main/java/com/ranya/conjexa/config/SecurityConfig.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/ranya/conjexa/config/SecurityConfig.java" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="FileTemplateManagerImpl">
    <option name="RECENT_TEMPLATES">
      <list>
        <option value="Enum" />
        <option value="Interface" />
        <option value="Class" />
      </list>
    </option>
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$/.." />
  </component>
  <component name="MarkdownSettingsMigration">
    <option name="stateVersion" value="1" />
  </component>
  <component name="MavenImportPreferences">
    <option name="generalSettings">
      <MavenGeneralSettings>
        <option name="mavenHome" value="Use Maven wrapper" />
      </MavenGeneralSettings>
    </option>
  </component>
  <component name="MavenRunner">
    <option name="skipTests" value="true" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 1
}</component>
  <component name="ProjectId" id="2zOx4hPc7gULJYl5VzgwyBE8rB8" />
  <component name="ProjectLevelVcsManager">
    <ConfirmationsSetting value="1" id="Add" />
  </component>
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;Maven.Conjexa [clean].executor&quot;: &quot;Run&quot;,
    &quot;Maven.Conjexa [compile].executor&quot;: &quot;Run&quot;,
    &quot;Maven.Conjexa [install].executor&quot;: &quot;Run&quot;,
    &quot;Maven.Conjexa [package].executor&quot;: &quot;Run&quot;,
    &quot;RequestMappingsPanelOrder0&quot;: &quot;0&quot;,
    &quot;RequestMappingsPanelOrder1&quot;: &quot;1&quot;,
    &quot;RequestMappingsPanelWidth0&quot;: &quot;75&quot;,
    &quot;RequestMappingsPanelWidth1&quot;: &quot;75&quot;,
    &quot;RunOnceActivity.OpenProjectViewOnStart&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;Spring Boot.ConjexaApplication.executor&quot;: &quot;Run&quot;,
    &quot;com.codeium.enabled&quot;: &quot;true&quot;,
    &quot;git-widget-placeholder&quot;: &quot;main&quot;,
    &quot;kotlin-language-version-configured&quot;: &quot;true&quot;,
    &quot;last_opened_file_path&quot;: &quot;C:/Stageete/eita_team/backend&quot;,
    &quot;node.js.detected.package.eslint&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.tslint&quot;: &quot;true&quot;,
    &quot;node.js.selected.package.eslint&quot;: &quot;(autodetect)&quot;,
    &quot;node.js.selected.package.tslint&quot;: &quot;(autodetect)&quot;,
    &quot;nodejs_package_manager_path&quot;: &quot;npm&quot;,
    &quot;settings.editor.selected.configurable&quot;: &quot;preferences.pluginManager&quot;,
    &quot;vue.rearranger.settings.migration&quot;: &quot;true&quot;
  }
}</component>
  <component name="RunManager">
    <configuration name="ConjexaApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="Conjexa" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.ranya.conjexa.ConjexaApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="SvnConfiguration">
    <configuration>C:\Users\<USER>\AppData\Roaming\Subversion</configuration>
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="a44b5110-00e2-47e2-9ec9-61df77808236" name="Changes" comment="" />
      <created>1751615731335</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1751615731335</updated>
      <workItem from="1751615732418" duration="2637000" />
      <workItem from="1751619653083" duration="21000" />
      <workItem from="1751619729578" duration="36000" />
      <workItem from="1751619781076" duration="32000" />
      <workItem from="1751619848978" duration="2633000" />
      <workItem from="1751623680011" duration="11000" />
      <workItem from="1751623706626" duration="1259000" />
      <workItem from="1751629533549" duration="948000" />
      <workItem from="1751631767670" duration="777000" />
      <workItem from="1751632962467" duration="103000" />
      <workItem from="1751633114632" duration="3445000" />
      <workItem from="1751637132609" duration="13000" />
      <workItem from="1751637343302" duration="1142000" />
      <workItem from="1751640517408" duration="21000" />
      <workItem from="1751641450284" duration="433000" />
      <workItem from="1751975947394" duration="1301000" />
      <workItem from="1752044747351" duration="1215000" />
      <workItem from="1752045992802" duration="1181000" />
      <workItem from="1752048239159" duration="4819000" />
      <workItem from="1752055996169" duration="39000" />
      <workItem from="1752056061885" duration="1249000" />
      <workItem from="1752057557389" duration="5106000" />
      <workItem from="1752135968576" duration="1020000" />
      <workItem from="1752137005299" duration="132000" />
      <workItem from="1752137190887" duration="24000" />
      <workItem from="1752137266513" duration="3540000" />
      <workItem from="1752142697204" duration="10048000" />
      <workItem from="1752218072932" duration="8431000" />
      <workItem from="1752763321791" duration="3711000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="Vcs.Log.Tabs.Properties">
    <option name="TAB_STATES">
      <map>
        <entry key="MAIN">
          <value>
            <State />
          </value>
        </entry>
      </map>
    </option>
  </component>
</project>