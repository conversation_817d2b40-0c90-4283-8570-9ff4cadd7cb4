package com.ranya.conjexa;

import io.swagger.v3.oas.annotations.OpenAPIDefinition;
import io.swagger.v3.oas.annotations.info.Info;
import io.swagger.v3.oas.annotations.servers.Server;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.scheduling.annotation.EnableScheduling;

@EnableAspectJAutoProxy
@EnableScheduling

@OpenAPIDefinition(
        info = @Info(
                title = "Conjexa API",
                version = "1.0",
                description = "API de gestion des congés avec Spring Boot + Keycloak"
        ),
        servers = {
                @Server(url = "http://localhost:8089/Conjexa", description = "Serveur local")
        }
)
@SpringBootApplication(scanBasePackages = "com.ranya.conjexa")
public class ConjexaApplication {

    public static void main(String[] args) {
        SpringApplication.run(ConjexaApplication.class, args);
    }

}
