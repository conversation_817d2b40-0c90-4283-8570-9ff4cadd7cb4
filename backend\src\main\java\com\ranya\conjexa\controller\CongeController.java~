package com.ranya.conjexa.controller;

import com.ranya.conjexa.entities.Conge;
import com.ranya.conjexa.entities.CreateCongeRequest;
import com.ranya.conjexa.services.CongeService;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api/conges")
@RequiredArgsConstructor
public class CongeController {
    private final CongeService congeService;
    @PostMapping("/create/{userId}")
    public ResponseEntity<Conge> createLeave(@PathVariable Long userId, @RequestBody CreateCongeRequest request) {
        return ResponseEntity.ok(congeService.createLeave(userId, request));
    }
    @PutMapping("/update/{id}")
    public ResponseEntity<Conge> updateLeave(@PathVariable Long id, @RequestBody Conge updatedLeave) {
        return ResponseEntity.ok(congeService.updateLeaveRequest(id, updatedLeave));
    }
    @PutMapping("/approve/{id}")
    public ResponseEntity<Conge> approve(@PathVariable Long id) {
        return ResponseEntity.ok(congeService.approve(id));
    }
    @PutMapping("/refuse/{id}")
    public ResponseEntity<Conge> refuse(@PathVariable Long id) {
        return ResponseEntity.ok(congeService.refuse(id));
    }
    @GetMapping("/{id}")
    public ResponseEntity<Conge> getById(@PathVariable Long id) {
        return ResponseEntity.ok(congeService.getLeaveById(id));
    }
    @GetMapping
    public ResponseEntity<List<Conge>> getAllConges() {
        return ResponseEntity.ok(congeService.getAllConges());
    }
    


}
