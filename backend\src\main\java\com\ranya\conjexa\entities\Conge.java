package com.ranya.conjexa.entities;

import com.fasterxml.jackson.annotation.JsonIgnore;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDate;
import java.time.LocalDateTime;

@Entity
@Setter
@Getter
public class Conge {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    private LocalDate dateDebut;
    private LocalDate dateFin;
    private String raison;
    @Enumerated(EnumType.STRING)
    private Statusconge status = Statusconge.En_attente;
    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    private Typeconge typeConge;
    @ManyToOne
    @JsonIgnore
    private User employee;
    private LocalDateTime datecreation ;
    @Column
    private LocalDateTime dateModification;
    @PrePersist
    protected void onCreate() {
        datecreation = LocalDateTime.now();
    }


}
