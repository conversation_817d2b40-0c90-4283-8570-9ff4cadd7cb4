package com.ranya.conjexa.services;

import com.ranya.conjexa.config.KeycloakProvider;
import com.ranya.conjexa.entities.CreateUserRequest;
import jakarta.ws.rs.core.Response;
import lombok.RequiredArgsConstructor;
import org.keycloak.admin.client.Keycloak;
import org.keycloak.representations.idm.*;

import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

@Service
@RequiredArgsConstructor
public class KeycloakAdminService {

    private final KeycloakProvider keycloakProvider;

    public String createKeycloakUser(CreateUserRequest request) {
        Keycloak keycloak = keycloakProvider.getInstance();


        List<UserRepresentation> existingUsers = keycloak.realm("Conjexa")
                .users()
                .search(request.getEmail());
        if (!existingUsers.isEmpty()) {
            throw new RuntimeException("L'utilisateur avec cet email existe déjà dans Keycloak.");
        }

        UserRepresentation user = new UserRepresentation();
        user.setUsername(request.getEmail());
        user.setEmail(request.getEmail());
        user.setFirstName(request.getPrenom());
        user.setLastName(request.getNom());
        user.setEnabled(true);
        user.setEmailVerified(true);


        Response response = keycloak.realm("Conjexa").users().create(user);

        if (response.getStatus() != 201) {
            throw new RuntimeException("Échec de création dans Keycloak: " + response.getStatus());
        }


        String userId = response.getLocation().getPath().replaceAll(".*/([^/]+)$", "$1");

        CredentialRepresentation credential = new CredentialRepresentation();
        credential.setTemporary(false);
        credential.setType(CredentialRepresentation.PASSWORD);
        credential.setValue(request.getPassword());

        keycloak.realm("Conjexa").users().get(userId).resetPassword(credential);

        RoleRepresentation role = keycloak.realm("Conjexa")
                .roles()
                .get("Employee")
                .toRepresentation();

        keycloak.realm("Conjexa")
                .users()
                .get(userId)
                .roles()
                .realmLevel()
                .add(Collections.singletonList(role));

        return userId;
    }

}

