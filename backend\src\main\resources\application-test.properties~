# Configuration pour les tests sans Keycloak
spring.application.name=Conjexa
server.port=8089

# Base de données
spring.datasource.url=******************************************
spring.datasource.username=postgres
spring.datasource.password=postgres
spring.jpa.hibernate.ddl-auto=update
spring.jpa.show-sql=true

# Désactiver la sécurité OAuth2 pour les tests
spring.autoconfigure.exclude=org.springframework.boot.autoconfigure.security.oauth2.resource.servlet.OAuth2ResourceServerAutoConfiguration
