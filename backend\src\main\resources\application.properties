spring.application.name=Conjexa
server.port=8089
server.servlet.context-path=/Conjexa
spring.datasource.url=******************************************
spring.datasource.username=postgres
spring.datasource.password=postgres
spring.jpa.hibernate.ddl-auto=update
spring.jpa.show-sql=true

spring.security.oauth2.resourceserver.jwt.issuer-uri=http://localhost:8085/realms/Conjexa
spring.security.oauth2.resourceserver.jwt.jwk-set-uri=http://localhost:8085/realms/Conjexa/protocol/openid-connect/certs
spring.security.oauth2.client.registration.keycloak.client-id=conjexa-rest-api
spring.security.oauth2.client.provider.keycloak.issuer-uri=http://localhost:8085/realms/Conjexa