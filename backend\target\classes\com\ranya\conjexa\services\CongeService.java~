package com.ranya.conjexa.services;

import com.ranya.conjexa.entities.Conge;
import com.ranya.conjexa.entities.CreateCongeRequest;
import com.ranya.conjexa.entities.Statusconge;
import com.ranya.conjexa.entities.User;
import com.ranya.conjexa.repository.CongeRepository;
import com.ranya.conjexa.repository.UserRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;

@Service
@RequiredArgsConstructor
public class CongeService {
    private final CongeRepository congeRepo;
    private final UserRepository userRepo;
    public Conge createLeave(Long userId, CreateCongeRequest request) {
        User employee = userRepo.findById(userId)
                .orElseThrow(() -> new RuntimeException("Utilisateur introuvable"));

        Conge conge = new Conge();
        conge.setEmployee(employee);
        conge.setStatus(Statusconge.En_attente);
        conge.setDateDebut(request.getDateDebut());
        conge.setDateFin(request.getDateFin());
        conge.setRaison(request.getRaison());
        conge.setTypeConge(request.getTypeConge());

        return congeRepo.save(conge);
    }

    public Conge approve(Long requestId) {
        Conge leave = getLeaveById(requestId);
        leave.setStatus(Statusconge.Accepte);
        leave.setDateModification(LocalDateTime.now());
        return congeRepo.save(leave);
    }

    public Conge refuse(Long requestId) {
        Conge leave = getLeaveById(requestId);
        leave.setStatus(Statusconge.Refuse);
        leave.setDateModification(LocalDateTime.now());
        return congeRepo.save(leave);
    }
    public Conge getLeaveById(Long requestId) {
        return congeRepo.findById(requestId)
                .orElseThrow(() -> new RuntimeException("Congé introuvable"));
    }
    public Conge updateLeaveRequest(Long id, Conge updatedLeave) {
        Conge existing = congeRepo.findById(id)
                .orElseThrow(() -> new RuntimeException("Demande introuvable"));

        if (existing.getStatus() != Statusconge.En_attente) {
            throw new RuntimeException("Impossible de modifier une demande déjà traitée.");
        }

        existing.setDateDebut(updatedLeave.getDateDebut());
        existing.setDateFin(updatedLeave.getDateFin());
        existing.setTypeConge(updatedLeave.getTypeConge());
        existing.setRaison(updatedLeave.getRaison());
        existing.setDateModification(LocalDateTime.now());

        return congeRepo.save(existing);
    }
    public List<Conge> getAllConges() {
        return congeRepo.findAll();
    }
    public void deleteLeave(Long id) {
        Conge conge = getLeaveById(id);
        System.out.println("Trying to delete leave with ID = " + id + ", Status = " + conge.getStatus());

        if (conge.getStatus() != Statusconge.En_attente) {
            throw new RuntimeException("Seuls les congés en attente peuvent être supprimés.");
        }
        congeRepo.delete(conge);
    }



}
