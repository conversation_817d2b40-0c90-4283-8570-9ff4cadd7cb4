/**
 * Conjexa API
 *
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


export interface Conge { 
    id?: number;
    dateDebut?: string;
    dateFin?: string;
    raison?: string;
    status?: Conge.StatusEnum;
    typeConge?: Conge.TypeCongeEnum;
    datecreation?: string;
    dateModification?: string;
}
export namespace Conge {
    export const StatusEnum = {
        EnAttente: 'En_attente',
        Accepte: 'Accepte',
        Refuse: 'Refuse'
    } as const;
    export type StatusEnum = typeof StatusEnum[keyof typeof StatusEnum];
    export const TypeCongeEnum = {
        Annuel: 'ANNUEL',
        Maladie: 'MALADIE',
        SansSolde: 'SANS_SOLDE'
    } as const;
    export type TypeCongeEnum = typeof TypeCongeEnum[keyof typeof TypeCongeEnum];
}


