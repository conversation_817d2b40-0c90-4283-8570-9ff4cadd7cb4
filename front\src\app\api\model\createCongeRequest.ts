/**
 * Conjexa API
 *
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


export interface CreateCongeRequest { 
    dateDebut?: string;
    dateFin?: string;
    raison?: string;
    typeConge?: CreateCongeRequest.TypeCongeEnum;
}
export namespace CreateCongeRequest {
    export const TypeCongeEnum = {
        Annuel: 'ANNUEL',
        Maladie: 'MALADIE',
        SansSolde: 'SANS_SOLDE'
    } as const;
    export type TypeCongeEnum = typeof TypeCongeEnum[keyof typeof TypeCongeEnum];
}


