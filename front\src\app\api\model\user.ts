/**
 * Conjexa API
 *
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


export interface User { 
    id?: number;
    nom?: string;
    prenom?: string;
    email?: string;
    keycloakId?: string;
    telephone?: string;
    password?: string;
    role?: User.RoleEnum;
    status?: boolean;
    active?: boolean;
    createdAt?: string;
}
export namespace User {
    export const RoleEnum = {
        Admin: 'Admin',
        Manager: 'Manager',
        Employee: 'Employee'
    } as const;
    export type RoleEnum = typeof RoleEnum[keyof typeof RoleEnum];
}


