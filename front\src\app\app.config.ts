import {
  ApplicationConfig,
  importProvidersFrom,
  provideBrowserGlobalErrorListeners,
  provideZoneChangeDetection
} from '@angular/core';
import { provideRouter } from '@angular/router';

import { routes } from './app.routes';
import {provideApi} from './api/provide-api';
import { HttpClientModule } from '@angular/common/http';

export * from './api/provide-api';




export const appConfig: ApplicationConfig = {
  providers: [
    provideBrowserGlobalErrorListeners(),
    provideZoneChangeDetection({ eventCoalescing: true }),
    provideRouter(routes),
    provideApi('http://localhost:8089/Conjexa'),
    importProvidersFrom(HttpClientModule),

  ]
};
