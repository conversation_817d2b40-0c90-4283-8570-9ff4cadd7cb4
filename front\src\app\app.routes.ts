import { Routes } from '@angular/router';
import {Dashboard} from './template/dashboard/dashboard';
import {adminGuard} from './guards/admin-guard';
import {Register} from './pages/register/register';
import {authGuard} from './guards/auth-guard';

export const routes: Routes = [ { path: 'register', component: Register, canActivate: [adminGuard] },
  { path: '', component: Dashboard, canActivate: [authGuard] },];
