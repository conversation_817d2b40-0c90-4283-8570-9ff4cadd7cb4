import { Routes } from '@angular/router';
import {Dashboard} from './template/dashboard/dashboard';
import {adminGuard} from './guards/admin-guard';
import {Register} from './pages/register/register';

export const routes: Routes = [
  { path: '', redirectTo: '/admin/dashboard', pathMatch: 'full' }, // Route par défaut
  { path: 'register', component: Register, canActivate: [adminGuard] },
  { path: 'admin/dashboard', component: Dashboard, canActivate: [adminGuard] },
  { path: 'unauthorized', component: Dashboard }, // Route temporaire pour les non-autorisés
  { path: '**', redirectTo: '/admin/dashboard' } // Route wildcard pour les 404
];
