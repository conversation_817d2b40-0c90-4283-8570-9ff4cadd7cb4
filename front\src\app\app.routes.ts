import { Routes } from '@angular/router';
import {Dashboard} from './template/dashboard/dashboard';
import {adminGuard} from './guards/admin-guard';
import {Register} from './pages/register/register';
import {Home} from './pages/home/<USER>';

export const routes: Routes = [
  { path: '', component: Home }, // Page d'accueil simple
  { path: 'home', component: Home },
  { path: 'register', component: Register, canActivate: [adminGuard] }, // Temporairement sans guard
  { path: 'admin/dashboard', component: Dashboard, canActivate: [adminGuard] }, // Temporairement sans guard
  { path: 'unauthorized', component: Home },
  { path: '**', redirectTo: '/home' } // Redirection vers home pour les 404
];
