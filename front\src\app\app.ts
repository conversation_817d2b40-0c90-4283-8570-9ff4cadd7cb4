import { Component, signal, OnInit, AfterViewInit } from '@angular/core';
import { RouterOutlet } from '@angular/router';
import { BootstrapInitService } from './services/bootstrap-init.service';

@Component({
  selector: 'app-root',
  imports: [RouterOutlet],
  templateUrl: './app.html',
  styleUrl: './app.css'
})
export class App implements OnInit, AfterViewInit {
  protected readonly title = signal('front');

  constructor(private bootstrapInitService: BootstrapInitService) {}

  ngOnInit(): void {
    // Initialisation au démarrage
  }

  ngAfterViewInit(): void {
    // Initialiser Bootstrap après que la vue soit rendue
    this.bootstrapInitService.initializeBootstrapComponents();
  }
}
