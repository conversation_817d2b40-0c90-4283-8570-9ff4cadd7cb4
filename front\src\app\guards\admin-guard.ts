import {CanActivate, CanActivateFn, Router} from '@angular/router';
import {Injectable} from '@angular/core';
import {KeycloakService} from 'keycloak-angular';

@Injectable({ providedIn: 'root' })
export class adminGuard implements CanActivate {
  constructor(private keycloak: KeycloakService, private router: Router) {}

  canActivate(): boolean {
    const isAdmin = this.keycloak.isUserInRole('Admin');
    if (!isAdmin) {
      this.router.navigate(['/unauthorized']);
    }
    return isAdmin;
  }
}
