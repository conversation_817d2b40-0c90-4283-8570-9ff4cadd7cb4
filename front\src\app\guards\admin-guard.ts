import {CanActivate, Router} from '@angular/router';
import {Injectable} from '@angular/core';
import {KeycloakService} from 'keycloak-angular';

@Injectable({ providedIn: 'root' })
export class adminGuard implements CanActivate {
  constructor(private keycloak: KeycloakService, private router: Router) {}

  async canActivate(): Promise<boolean> {
    try {
      // Vérifier si l'utilisateur est authentifié
      const isLoggedIn = this.keycloak.isLoggedIn();

      if (!isLoggedIn) {
        // Rediriger vers la page de connexion Keycloak
        await this.keycloak.login({
          redirectUri: window.location.origin + window.location.pathname
        });
        return false;
      }

      // Vérifier si l'utilisateur a le rôle Admin
      const isAdmin = this.keycloak.isUserInRole('Admin');

      if (!isAdmin) {
        console.warn('Utilisateur connecté mais sans rôle Admin');
        this.router.navigate(['/unauthorized']);
        return false;
      }

      return true;
    } catch (error) {
      console.error('Erreur dans adminGuard:', error);
      this.router.navigate(['/unauthorized']);
      return false;
    }
  }
}
