import { CanActivate, Router } from '@angular/router';
import { Injectable } from '@angular/core';
import { KeycloakService } from 'keycloak-angular';

@Injectable({ providedIn: 'root' })
export class adminGuard implements CanActivate {
  constructor(private keycloak: KeycloakService, private router: Router) {}

  async canActivate(): Promise<boolean> {
    await this.keycloak.isLoggedIn(); // s'assurer que c'est bien initialisé
    const isAdmin = this.keycloak.isUserInRole('Admin');

    if (!isAdmin) {
      this.router.navigate(['/unauthorized']);
      return false;
    }
    return true;
  }
}
