import {CanActivate, Router} from '@angular/router';
import {Injectable} from '@angular/core';
import {KeycloakService} from 'keycloak-angular';

@Injectable({ providedIn: 'root' })
export class AuthGuard implements CanActivate {
  constructor(private keycloak: KeycloakService, private router: Router) {}

  async canActivate(): Promise<boolean> {
    try {
      console.log('AuthGuard: Vérification de l\'authentification...');
      
      // Vérifier si Keycloak est initialisé
      const isInitialized = this.keycloak.getKeycloakInstance();
      if (!isInitialized) {
        console.error('Keycloak n\'est pas initialisé');
        return false;
      }

      // Vérifier si l'utilisateur est authentifié
      const isLoggedIn = this.keycloak.isLoggedIn();
      console.log('Utilisateur connecté:', isLoggedIn);
      
      if (!isLoggedIn) {
        console.log('Redirection vers la page de connexion Keycloak...');
        // Rediriger vers la page de connexion Keycloak
        await this.keycloak.login({
          redirectUri: window.location.origin + window.location.pathname
        });
        return false;
      }

      // Afficher les informations utilisateur pour debug
      const userProfile = this.keycloak.getUsername();
      const userRoles = this.keycloak.getUserRoles();
      console.log('Utilisateur:', userProfile);
      console.log('Rôles:', userRoles);

      return true;
    } catch (error) {
      console.error('Erreur dans AuthGuard:', error);
      return false;
    }
  }
}
