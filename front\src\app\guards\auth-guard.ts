import {inject, Injectable} from '@angular/core';
import { CanActivateFn } from '@angular/router';
import { KeycloakService } from 'keycloak-angular';

export const authGuard: CanActivateFn = async (route, state) => {
  const keycloak = inject(KeycloakService);
  const loggedIn = await keycloak.isLoggedIn();

  if (!loggedIn) {
    await keycloak.login(); // ← redirige vers Keycloak
    return false;
  }

  return true;
};
