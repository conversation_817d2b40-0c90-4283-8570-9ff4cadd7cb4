import {Injectable} from '@angular/core';
import {CanActivate, Router} from '@angular/router';
import {KeycloakService} from 'keycloak-angular';

@Injectable({ providedIn: 'root' })
export class employeeGuard implements CanActivate {
  constructor(private keycloak: KeycloakService, private router: Router) {}

  canActivate(): boolean {
    const isEmployee = this.keycloak.isUserInRole('Employee');
    if (!isEmployee) {
      this.router.navigate(['/unauthorized']);
    }
    return isEmployee;
  }
}
