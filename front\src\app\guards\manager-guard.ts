import {Injectable} from '@angular/core';
import {CanActivate, Router} from '@angular/router';
import {KeycloakService} from 'keycloak-angular';

@Injectable({ providedIn: 'root' })
export class managerGuard implements CanActivate {
  constructor(private keycloak: KeycloakService, private router: Router) {}

  canActivate(): boolean {
    const isManager = this.keycloak.isUserInRole('Manager');
    if (!isManager) {
      this.router.navigate(['/unauthorized']);
    }
    return isManager;
  }
}
