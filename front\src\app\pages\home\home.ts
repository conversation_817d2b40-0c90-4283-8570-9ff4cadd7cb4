import { Component } from '@angular/core';

@Component({
  selector: 'app-home',
  standalone: true,
  imports: [],
  template: `
    <div class="container mt-5">
      <div class="row justify-content-center">
        <div class="col-md-8">
          <div class="card">
            <div class="card-header">
              <h3 class="text-center">Bienvenue dans l'application Conjexa</h3>
            </div>
            <div class="card-body">
              <p class="text-center">Application de gestion des congés</p>
              <div class="text-center">
                <a href="/admin/dashboard" class="btn btn-primary me-2">Dashboard</a>
                <a href="/register" class="btn btn-secondary">S'inscrire</a>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  `,
  styles: [`
    .card {
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    }
  `]
})
export class Home {
}
