<h2>Ajouter un employé</h2>

<form (ngSubmit)="onSubmit()" #registerForm="ngForm">
  <label>Nom:</label>
  <input type="text" [(ngModel)]="user.nom" name="nom" required>

  <label>Prénom:</label>
  <input type="text" [(ngModel)]="user.prenom" name="prenom" required>

  <label>Email:</label>
  <input type="email" [(ngModel)]="user.email" name="email" required>

  <label>Mot de passe:</label>
  <input type="password" [(ngModel)]="user.password" name="password" required>

  <label>Téléphone:</label>
  <input type="text" [(ngModel)]="user.telephone" name="telephone">

  <button type="submit">Ajouter</button>
</form>
