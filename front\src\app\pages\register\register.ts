import { Component } from '@angular/core';
import {CreateUserRequest, UserControllerService} from '../../api';
import {Router} from '@angular/router';
import {FormsModule} from '@angular/forms';

@Component({
  selector: 'app-register',
  imports: [
    FormsModule
  ],
  templateUrl: './register.html',
  styleUrl: './register.css'
})
export class Register {
  user: CreateUserRequest = {
    nom: '',
    prenom: '',
    email: '',
    password: '',
    telephone: ''

};
  constructor(
    private userService: UserControllerService,
    private router: Router
  ) {}
  onSubmit() {
    this.userService.createUser(this.user).subscribe({
      next: () => {
        alert("Utilisateur créé avec succès !");
        this.router.navigate(['']);
      },
      error: (err) => {
        console.error(err);
        alert("Erreur lors de la création");
      }
    });
  }
}
