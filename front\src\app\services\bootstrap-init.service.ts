import { Injectable } from '@angular/core';

declare var bootstrap: any;
declare var $: any;

@Injectable({
  providedIn: 'root'
})
export class BootstrapInitService {

  constructor() { }

  initializeBootstrapComponents(): void {
    // Attendre que le DOM soit prêt
    setTimeout(() => {
      this.initTooltips();
      this.initPopovers();
    }, 100);
  }

  private initTooltips(): void {
    try {
      // Support pour les deux syntaxes Bootstrap 4 et 5
      const tooltipSelectors = '[data-bs-toggle="tooltip"], [data-toggle="tooltip"]';
      const tooltipTriggerList = Array.from(document.querySelectorAll(tooltipSelectors));
      
      tooltipTriggerList.forEach(tooltipTriggerEl => {
        new bootstrap.Tooltip(tooltipTriggerEl);
      });
    } catch (error) {
      console.warn('Erreur lors de l\'initialisation des tooltips:', error);
    }
  }

  private initPopovers(): void {
    try {
      // Support pour les deux syntaxes Bootstrap 4 et 5
      const popoverSelectors = '[data-bs-toggle="popover"], [data-toggle="popover"]';
      const popoverTriggerList = Array.from(document.querySelectorAll(popoverSelectors));
      
      popoverTriggerList.forEach(popoverTriggerEl => {
        new bootstrap.Popover(popoverTriggerEl);
      });
    } catch (error) {
      console.warn('Erreur lors de l\'initialisation des popovers:', error);
    }
  }

  // Méthode pour réinitialiser les composants après des changements dynamiques
  reinitializeComponents(): void {
    this.initializeBootstrapComponents();
  }
}
