import { Injectable } from '@angular/core';

export interface ToastMessage {
  id: string;
  type: 'success' | 'error' | 'warning' | 'info';
  title: string;
  message: string;
  duration?: number;
}

@Injectable({
  providedIn: 'root'
})
export class ToastService {
  private toasts: ToastMessage[] = [];

  constructor() { }

  // Afficher un toast de succès
  showSuccess(title: string, message: string, duration: number = 5000) {
    this.addToast('success', title, message, duration);
  }

  // Afficher un toast d'erreur
  showError(title: string, message: string, duration: number = 7000) {
    this.addToast('error', title, message, duration);
  }

  // Afficher un toast d'avertissement
  showWarning(title: string, message: string, duration: number = 6000) {
    this.addToast('warning', title, message, duration);
  }

  // Afficher un toast d'information
  showInfo(title: string, message: string, duration: number = 5000) {
    this.addToast('info', title, message, duration);
  }

  // Ajouter un toast
  private addToast(type: ToastMessage['type'], title: string, message: string, duration: number) {
    const id = this.generateId();
    const toast: ToastMessage = {
      id,
      type,
      title,
      message,
      duration
    };

    this.toasts.push(toast);
    this.createToastElement(toast);

    // Auto-suppression après la durée spécifiée
    setTimeout(() => {
      this.removeToast(id);
    }, duration);
  }

  // Créer l'élément DOM du toast
  private createToastElement(toast: ToastMessage) {
    // Créer le conteneur de toasts s'il n'existe pas
    let container = document.getElementById('toast-container');
    if (!container) {
      container = document.createElement('div');
      container.id = 'toast-container';
      container.className = 'toast-container position-fixed top-0 end-0 p-3';
      container.style.zIndex = '9999';
      document.body.appendChild(container);
    }

    // Créer le toast
    const toastElement = document.createElement('div');
    toastElement.id = `toast-${toast.id}`;
    toastElement.className = `toast align-items-center text-white bg-${this.getBootstrapColor(toast.type)} border-0 show`;
    toastElement.setAttribute('role', 'alert');
    toastElement.innerHTML = `
      <div class="d-flex">
        <div class="toast-body">
          <strong>${toast.title}</strong><br>
          ${toast.message}
        </div>
        <button type="button" class="btn-close btn-close-white me-2 m-auto" onclick="document.getElementById('toast-${toast.id}').remove()"></button>
      </div>
    `;

    container.appendChild(toastElement);

    // Animation d'entrée
    setTimeout(() => {
      toastElement.classList.add('fade-in');
    }, 100);
  }

  // Supprimer un toast
  private removeToast(id: string) {
    const toastElement = document.getElementById(`toast-${id}`);
    if (toastElement) {
      toastElement.classList.add('fade-out');
      setTimeout(() => {
        toastElement.remove();
      }, 300);
    }
    this.toasts = this.toasts.filter(t => t.id !== id);
  }

  // Obtenir la couleur Bootstrap correspondante
  private getBootstrapColor(type: ToastMessage['type']): string {
    switch (type) {
      case 'success': return 'success';
      case 'error': return 'danger';
      case 'warning': return 'warning';
      case 'info': return 'info';
      default: return 'primary';
    }
  }

  // Générer un ID unique
  private generateId(): string {
    return Math.random().toString(36).substr(2, 9);
  }

  // Obtenir tous les toasts
  getToasts(): ToastMessage[] {
    return this.toasts;
  }

  // Supprimer tous les toasts
  clearAll() {
    this.toasts.forEach(toast => this.removeToast(toast.id));
  }
}
