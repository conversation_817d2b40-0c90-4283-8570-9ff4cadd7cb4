/* Styles pour le bouton Ajouter Employé */
.add-employee-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  border-radius: 25px;
  padding: 8px 20px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.add-employee-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
  background: linear-gradient(135deg, #764ba2 0%, #667eea 100%);
}

/* Styles pour le modal */
.modal-content {
  border-radius: 20px;
  overflow: hidden;
}

.modal-header.bg-gradient-primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
}

.modal-body {
  background: #f8f9fa;
}

.form-control-lg {
  border-radius: 12px;
  border: 2px solid #e9ecef;
  padding: 12px 16px;
  transition: all 0.3s ease;
}

.form-control-lg:focus {
  border-color: #667eea;
  box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
  transform: translateY(-1px);
}

.form-label {
  color: #495057;
  margin-bottom: 8px;
}

.form-label i {
  font-size: 1.1em;
}

.alert-info.bg-light-info {
  background-color: #e3f2fd !important;
  color: #1976d2;
  border-left: 4px solid #2196f3;
}

.btn-lg {
  border-radius: 12px;
  padding: 12px 24px;
  font-weight: 600;
  transition: all 0.3s ease;
}

.btn-primary.btn-lg {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
}

.btn-primary.btn-lg:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.btn-light.btn-lg:hover {
  background-color: #e2e6ea;
  transform: translateY(-1px);
}

.invalid-feedback {
  font-size: 0.875em;
  margin-top: 5px;
}

.is-invalid {
  border-color: #dc3545 !important;
  animation: shake 0.5s ease-in-out;
}

@keyframes shake {
  0%, 100% { transform: translateX(0); }
  25% { transform: translateX(-5px); }
  75% { transform: translateX(5px); }
}

/* Animation d'apparition du modal */
.modal.fade .modal-dialog {
  transform: scale(0.8) translateY(-50px);
  transition: all 0.3s ease;
}

.modal.show .modal-dialog {
  transform: scale(1) translateY(0);
}

/* Responsive */
@media (max-width: 768px) {
  .add-employee-btn {
    padding: 6px 12px;
    font-size: 0.875rem;
  }

  .modal-lg {
    max-width: 95%;
  }

  .form-control-lg {
    padding: 10px 14px;
  }
}

/* Styles pour les toasts */
.toast-container {
  z-index: 9999;
}

.toast {
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  margin-bottom: 10px;
  opacity: 0;
  transform: translateX(100%);
  transition: all 0.3s ease;
}

.toast.fade-in {
  opacity: 1;
  transform: translateX(0);
}

.toast.fade-out {
  opacity: 0;
  transform: translateX(100%);
}

.toast-body {
  padding: 12px 16px;
  font-size: 0.9rem;
}

.toast-body strong {
  font-weight: 600;
  margin-bottom: 4px;
  display: block;
}