<!-- loader removed for Angular -->
<!-- Wrapper Start -->
<div class="wrapper">

  <div class="iq-sidebar  sidebar-default ">
    <div class="iq-sidebar-logo d-flex align-items-center justify-content-between">
      <a href="index.html" class="header-logo">
        <img ngSrc="assets/images/logo.png" class="img-fluid rounded-normal light-logo" alt="logo"
             style="height: auto; width: auto; max-height: 40px;"><h5 class="logo-title light-logo ml-3">POSDash</h5>
      </a>
      <div class="iq-menu-bt-sidebar ml-0">
        <i class="las la-bars wrapper-menu"></i>
      </div>
    </div>
    <div class="data-scrollbar" data-scroll="1">
      <nav class="iq-sidebar-menu">
        <ul id="iq-sidebar-toggle" class="iq-menu">
          <li class="active">
            <a href="index.html" class="svg-icon">
              <svg  class="svg-icon" id="p-dash1" width="20" height="20" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path d="M21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16z"></path><polyline points="3.27 6.96 12 12.01 20.73 6.96"></polyline><line x1="12" y1="22.08" x2="12" y2="12"></line>
              </svg>
              <span class="ml-4">Dashboards</span>
            </a>
          </li>
          <li class=" ">
            <a href="#product" class="collapsed" data-toggle="collapse" aria-expanded="false">
              <svg class="svg-icon" id="p-dash2" width="20" height="20"  xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="9" cy="21" r="1"></circle><circle cx="20" cy="21" r="1"></circle>
                <path d="M1 1h4l2.68 13.39a2 2 0 0 0 2 1.61h9.72a2 2 0 0 0 2-1.61L23 6H6"></path>
              </svg>
              <span class="ml-4">Products</span>
              <svg class="svg-icon iq-arrow-right arrow-active" width="20" height="20" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <polyline points="10 15 15 20 20 15"></polyline><path d="M4 4h7a4 4 0 0 1 4 4v12"></path>
              </svg>
            </a>
          </li>
          <li class=" ">
            <a href="#category" class="collapsed" data-toggle="collapse" aria-expanded="false">
              <svg class="svg-icon" id="p-dash3" width="20" height="20" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><rect x="9" y="9" width="13" height="13" rx="2" ry="2"></rect>
                <path d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"></path>
              </svg>
              <span class="ml-4">Categories</span>
              <svg class="svg-icon iq-arrow-right arrow-active" width="20" height="20" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <polyline points="10 15 15 20 20 15"></polyline><path d="M4 4h7a4 4 0 0 1 4 4v12"></path>
              </svg>
            </a>
          </li>
          <li class=" ">
            <a href="#sale" class="collapsed" data-toggle="collapse" aria-expanded="false">
              <svg class="svg-icon" id="p-dash4" width="20" height="20" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path d="M21.21 15.89A10 10 0 1 1 8 2.83"></path><path d="M22 12A10 10 0 0 0 12 2v10z"></path>
              </svg>
              <span class="ml-4">Sale</span>
              <svg class="svg-icon iq-arrow-right arrow-active" width="20" height="20" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <polyline points="10 15 15 20 20 15"></polyline><path d="M4 4h7a4 4 0 0 1 4 4v12"></path>
              </svg>
            </a>
          </li>
          <li class=" ">
            <a href="#purchase" class="collapsed" data-toggle="collapse" aria-expanded="false">
              <svg class="svg-icon" id="p-dash5" width="20" height="20" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><rect x="1" y="4" width="22" height="16" rx="2" ry="2"></rect>
                <line x1="1" y1="10" x2="23" y2="10"></line>
              </svg>
              <span class="ml-4">Purchases</span>
              <svg class="svg-icon iq-arrow-right arrow-active" width="20" height="20" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <polyline points="10 15 15 20 20 15"></polyline><path d="M4 4h7a4 4 0 0 1 4 4v12"></path>
              </svg>
            </a>
          </li>
          <li class=" ">
            <a href="#return" class="collapsed" data-toggle="collapse" aria-expanded="false">
              <svg class="svg-icon" id="p-dash6" width="20" height="20" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <polyline points="4 14 10 14 10 20"></polyline><polyline points="20 10 14 10 14 4"></polyline><line x1="14" y1="10" x2="21" y2="3"></line><line x1="3" y1="21" x2="10" y2="14"></line>
              </svg>
              <span class="ml-4">Returns</span>
              <svg class="svg-icon iq-arrow-right arrow-active" width="20" height="20" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <polyline points="10 15 15 20 20 15"></polyline><path d="M4 4h7a4 4 0 0 1 4 4v12"></path>
              </svg>
            </a>
          </li>
          <li class=" ">
            <a href="#people" class="collapsed" data-toggle="collapse" aria-expanded="false">
              <svg class="svg-icon" id="p-dash8" width="20" height="20" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"></path><circle cx="9" cy="7" r="4"></circle><path d="M23 21v-2a4 4 0 0 0-3-3.87"></path><path d="M16 3.13a4 4 0 0 1 0 7.75"></path>
              </svg>
              <span class="ml-4">People</span>
              <svg class="svg-icon iq-arrow-right arrow-active" width="20" height="20" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <polyline points="10 15 15 20 20 15"></polyline><path d="M4 4h7a4 4 0 0 1 4 4v12"></path>
              </svg>
            </a>

          </li>

          <li class=" ">
            <a href="#otherpage" class="collapsed" data-toggle="collapse" aria-expanded="false">
              <svg class="svg-icon" id="p-dash9" width="20" height="20" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect><rect x="7" y="7" width="3" height="9"></rect><rect x="14" y="7" width="3" height="5"></rect>
              </svg>
              <span class="ml-4">other page</span>
              <svg class="svg-icon iq-arrow-right arrow-active" width="20" height="20" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <polyline points="10 15 15 20 20 15"></polyline><path d="M4 4h7a4 4 0 0 1 4 4v12"></path>
              </svg>
            </a>
            <ul id="otherpage" class="iq-submenu collapse" data-parent="#iq-sidebar-toggle">
              <li class=" ">
                <a href="#user" class="collapsed" data-toggle="collapse" aria-expanded="false">
                  <svg class="svg-icon" id="p-dash10" width="20" height="20" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <path d="M16 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"></path><circle cx="8.5" cy="7" r="4"></circle><polyline points="17 11 19 13 23 9"></polyline>
                  </svg>
                  <span class="ml-4">User Details</span>
                  <svg class="svg-icon iq-arrow-right arrow-active" width="20" height="20" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <polyline points="10 15 15 20 20 15"></polyline><path d="M4 4h7a4 4 0 0 1 4 4v12"></path>
                  </svg>
                </a>
              </li>
              <li class=" ">
                <a href="#ui" class="collapsed" data-toggle="collapse" aria-expanded="false">
                  <svg class="svg-icon" id="p-dash11" width="20" height="20" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <path d="M21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16z"></path>
                  </svg>
                  <span class="ml-4">UI Elements</span>
                  <svg class="svg-icon iq-arrow-right arrow-active" width="20" height="20" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <polyline points="10 15 15 20 20 15"></polyline><path d="M4 4h7a4 4 0 0 1 4 4v12"></path>
                  </svg>
                </a>
              </li>
              <li class=" ">
                <a href="#auth" class="collapsed" data-toggle="collapse" aria-expanded="false">
                  <svg class="svg-icon" id="p-dash12" width="20" height="20" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path><polyline points="14 2 14 8 20 8"></polyline><line x1="16" y1="13" x2="8" y2="13"></line><line x1="16" y1="17" x2="8" y2="17"></line><polyline points="10 9 9 9 8 9"></polyline>
                  </svg>
                  <span class="ml-4">Authentication</span>
                  <svg class="svg-icon iq-arrow-right arrow-active" width="20" height="20" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <polyline points="10 15 15 20 20 15"></polyline><path d="M4 4h7a4 4 0 0 1 4 4v12"></path>
                  </svg>
                </a>
              </li>
              <li class="">
                <a href="#form" class="collapsed svg-icon" data-toggle="collapse" aria-expanded="false">
                  <svg class="svg-icon" id="p-dash13" width="20" height="20"  xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <path d="M16 4h2a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2h2"></path><rect x="8" y="2" width="8" height="4" rx="1" ry="1"></rect>
                  </svg>
                  <span class="ml-4">Forms</span>
                  <svg class="svg-icon iq-arrow-right arrow-active" width="20" height="20" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <polyline points="10 15 15 20 20 15"></polyline><path d="M4 4h7a4 4 0 0 1 4 4v12"></path>
                  </svg>
                </a>
              </li>
              <li class=" ">
                <a href="#table" class="collapsed" data-toggle="collapse" aria-expanded="false">
                  <svg class="svg-icon" id="p-dash14" width="20" height="20" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <rect x="3" y="3" width="7" height="7"></rect><rect x="14" y="3" width="7" height="7"></rect><rect x="14" y="14" width="7" height="7"></rect><rect x="3" y="14" width="7" height="7"></rect>
                  </svg>
                  <span class="ml-4">Table</span>
                  <svg class="svg-icon iq-arrow-right arrow-active" width="20" height="20" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <polyline points="10 15 15 20 20 15"></polyline><path d="M4 4h7a4 4 0 0 1 4 4v12"></path>
                  </svg>
                </a>
              </li>
              <li class=" ">
                <a href="#pricing" class="collapsed" data-toggle="collapse" aria-expanded="false">
                  <svg class="svg-icon" id="p-dash16" width="20" height="20" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <ellipse cx="12" cy="5" rx="9" ry="3"></ellipse><path d="M21 12c0 1.66-4 3-9 3s-9-1.34-9-3"></path><path d="M3 5v14c0 1.66 4 3 9 3s9-1.34 9-3V5"></path>
                  </svg>
                  <span class="ml-4">Pricing</span>
                  <svg class="svg-icon iq-arrow-right arrow-active" width="20" height="20" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <polyline points="10 15 15 20 20 15"></polyline><path d="M4 4h7a4 4 0 0 1 4 4v12"></path>
                  </svg>
                </a>
              </li>
            </ul>
          </li>
        </ul>
      </nav>
      <div id="sidebar-bottom" class="position-relative sidebar-bottom">
        <div class="card border-none">
          <div class="card-body p-0">
            <div class="sidebarbottom-content">
              <div class="image"><img ngSrc="assets/images/layouts/side-bkg.png" class="img-fluid" alt="side-bkg"
                                      height="183" width="175"></div>
              <h6 class="mt-4 px-4 body-title"></h6>

            </div>
          </div>
        </div>
      </div>
      <div class="p-3"></div>
    </div>
  </div>      <div class="iq-top-navbar">
  <div class="iq-navbar-custom">
    <nav class="navbar navbar-expand-lg navbar-light p-0">
      <div class="iq-navbar-logo d-flex align-items-center justify-content-between">
        <i class="ri-menu-line wrapper-menu"></i>
        <a href="index.html" class="header-logo">
          <img ngSrc="assets/images/logo.png" class="img-fluid rounded-normal" alt="logo"
               style="height: auto; width: auto; max-height: 40px;">
          <h5 class="logo-title ml-3">POSDash</h5>

        </a>
      </div>
      <div class="iq-search-bar device-search">
        <form action="#" class="searchbox">
          <a class="search-link" href="#"><i class="ri-search-line"></i></a>
          <input type="text" class="text search-input" placeholder="Search here...">
        </form>
      </div>
      <div class="d-flex align-items-center">
        <button class="navbar-toggler" type="button" data-toggle="collapse"
                data-target="#navbarSupportedContent" aria-controls="navbarSupportedContent"
                aria-label="Toggle navigation">
          <i class="ri-menu-3-line"></i>
        </button>
        <div class="collapse navbar-collapse" id="navbarSupportedContent">
          <ul class="navbar-nav ml-auto navbar-list align-items-center">
            <!-- Bouton Ajouter Employé -->
            <li class="nav-item">
              <button type="button" class="btn btn-primary btn-sm me-3 add-employee-btn"
                      data-bs-toggle="modal" data-bs-target="#addEmployeeModal">
                <i class="ri-user-add-line me-1"></i>
                Ajouter Employé
              </button>
            </li>
            <li class="nav-item nav-icon dropdown">
              <a href="#" class="search-toggle dropdown-toggle btn border add-btn"
                 id="dropdownMenuButton02" data-toggle="dropdown" aria-haspopup="true"
                 aria-expanded="false">
                <img ngSrc="assets/images/small/flag-01.png" alt="img-flag"
                     class="img-fluid image-flag mr-2" height="16" width="16">En
              </a>
              <div class="iq-sub-dropdown dropdown-menu" aria-labelledby="dropdownMenuButton2">
                <div class="card shadow-none m-0">
                  <div class="card-body p-3">
                    <a class="iq-sub-card" href="#"><img
                      ngSrc="assets/images/small/flag-02.png" alt="img-flag"
                      class="img-fluid mr-2" height="16" width="16">French</a>
                    <a class="iq-sub-card" href="#"><img
                      ngSrc="assets/images/small/flag-03.png" alt="img-flag"
                      class="img-fluid mr-2" height="16" width="16">Spanish</a>
                    <a class="iq-sub-card" href="#"><img
                      ngSrc="assets/images/small/flag-04.png" alt="img-flag"
                      class="img-fluid mr-2" height="16" width="16">Italian</a>
                    <a class="iq-sub-card" href="#"><img
                      ngSrc="assets/images/small/flag-05.png" alt="img-flag"
                      class="img-fluid mr-2" height="16" width="16">German</a>
                    <a class="iq-sub-card" href="#"><img
                      ngSrc="assets/images/small/flag-06.png" alt="img-flag"
                      class="img-fluid mr-2" height="16" width="16">Japanese</a>
                  </div>
                </div>
              </div>
            </li>
            <li>
              <a href="#" class="btn border add-btn shadow-none mx-2 d-none d-md-block"
                 data-toggle="modal" data-target="#new-order"><i class="las la-plus mr-2"></i>New
                Order</a>
            </li>
            <li class="nav-item nav-icon search-content">
              <a href="#" class="search-toggle rounded" id="dropdownSearch" data-toggle="dropdown"
                 aria-haspopup="true" aria-expanded="false">
                <i class="ri-search-line"></i>
              </a>
              <div class="iq-search-bar iq-sub-dropdown dropdown-menu" aria-labelledby="dropdownSearch">
                <form action="#" class="searchbox p-2">
                  <div class="form-group mb-0 position-relative">
                    <input type="text" class="text search-input font-size-12"
                           placeholder="type here to search...">
                    <a href="#" class="search-link"><i class="las la-search"></i></a>
                  </div>
                </form>
              </div>
            </li>
            <li class="nav-item nav-icon dropdown">
              <a href="#" class="search-toggle dropdown-toggle" id="dropdownMenuButton2"
                 data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24"
                     fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                     stroke-linejoin="round" class="feather feather-mail">
                  <path
                    d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z">
                  </path>
                  <polyline points="22,6 12,13 2,6"></polyline>
                </svg>
                <span class="bg-primary"></span>
              </a>
              <div class="iq-sub-dropdown dropdown-menu" aria-labelledby="dropdownMenuButton2">
                <div class="card shadow-none m-0">
                  <div class="card-body p-0 ">
                    <div class="cust-title p-3">
                      <div class="d-flex align-items-center justify-content-between">
                        <h5 class="mb-0">All Messages</h5>
                        <a class="badge badge-primary badge-card" href="#">3</a>
                      </div>
                    </div>
                    <div class="px-3 pt-0 pb-0 sub-card">
                      <a href="#" class="iq-sub-card">
                        <div class="media align-items-center cust-card py-3 border-bottom">
                          <div class="">
                            <img class="avatar-50 rounded-small"
                                 ngSrc="assets/images/user/01.jpg" alt="01" height="350" width="350">
                          </div>
                          <div class="media-body ml-3">
                            <div class="d-flex align-items-center justify-content-between">
                              <h6 class="mb-0">Emma Watson</h6>
                              <small class="text-dark"><b>12 : 47 pm</b></small>
                            </div>
                            <small class="mb-0">Lorem ipsum dolor sit amet</small>
                          </div>
                        </div>
                      </a>
                      <a href="#" class="iq-sub-card">
                        <div class="media align-items-center cust-card py-3 border-bottom">
                          <div class="">
                            <img class="avatar-50 rounded-small"
                                 ngSrc="assets/images/user/02.jpg" alt="02" height="350" width="350">
                          </div>
                          <div class="media-body ml-3">
                            <div class="d-flex align-items-center justify-content-between">
                              <h6 class="mb-0">Ashlynn Franci</h6>
                              <small class="text-dark"><b>11 : 30 pm</b></small>
                            </div>
                            <small class="mb-0">Lorem ipsum dolor sit amet</small>
                          </div>
                        </div>
                      </a>
                      <a href="#" class="iq-sub-card">
                        <div class="media align-items-center cust-card py-3">
                          <div class="">
                            <img class="avatar-50 rounded-small"
                                 ngSrc="assets/images/user/03.jpg" alt="03" height="350" width="350">
                          </div>
                          <div class="media-body ml-3">
                            <div class="d-flex align-items-center justify-content-between">
                              <h6 class="mb-0">Kianna Carder</h6>
                              <small class="text-dark"><b>11 : 21 pm</b></small>
                            </div>
                            <small class="mb-0">Lorem ipsum dolor sit amet</small>
                          </div>
                        </div>
                      </a>
                    </div>
                    <a class="right-ic btn btn-primary btn-block position-relative p-2" href="#"
                       role="button">
                      View All
                    </a>
                  </div>
                </div>
              </div>
            </li>
            <li class="nav-item nav-icon dropdown">
              <a href="#" class="search-toggle dropdown-toggle" id="dropdownMenuButton"
                 data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24"
                     fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                     stroke-linejoin="round" class="feather feather-bell">
                  <path d="M18 8A6 6 0 0 0 6 8c0 7-3 9-3 9h18s-3-2-3-9"></path>
                  <path d="M13.73 21a2 2 0 0 1-3.46 0"></path>
                </svg>
                <span class="bg-primary "></span>
              </a>
              <div class="iq-sub-dropdown dropdown-menu" aria-labelledby="dropdownMenuButton">
                <div class="card shadow-none m-0">
                  <div class="card-body p-0 ">
                    <div class="cust-title p-3">
                      <div class="d-flex align-items-center justify-content-between">
                        <h5 class="mb-0">Notifications</h5>
                        <a class="badge badge-primary badge-card" href="#">3</a>
                      </div>
                    </div>
                    <div class="px-3 pt-0 pb-0 sub-card">
                      <a href="#" class="iq-sub-card">
                        <div class="media align-items-center cust-card py-3 border-bottom">
                          <div class="">
                            <img class="avatar-50 rounded-small"
                                 ngSrc="assets/images/user/01.jpg" alt="01" height="350" width="350">
                          </div>
                          <div class="media-body ml-3">
                            <div class="d-flex align-items-center justify-content-between">
                              <h6 class="mb-0">Emma Watson</h6>
                              <small class="text-dark"><b>12 : 47 pm</b></small>
                            </div>
                            <small class="mb-0">Lorem ipsum dolor sit amet</small>
                          </div>
                        </div>
                      </a>
                      <a href="#" class="iq-sub-card">
                        <div class="media align-items-center cust-card py-3 border-bottom">
                          <div class="">
                            <img class="avatar-50 rounded-small"
                                 ngSrc="assets/images/user/02.jpg" alt="02" height="350" width="350">
                          </div>
                          <div class="media-body ml-3">
                            <div class="d-flex align-items-center justify-content-between">
                              <h6 class="mb-0">Ashlynn Franci</h6>
                              <small class="text-dark"><b>11 : 30 pm</b></small>
                            </div>
                            <small class="mb-0">Lorem ipsum dolor sit amet</small>
                          </div>
                        </div>
                      </a>
                      <a href="#" class="iq-sub-card">
                        <div class="media align-items-center cust-card py-3">
                          <div class="">
                            <img class="avatar-50 rounded-small"
                                 ngSrc="assets/images/user/03.jpg" alt="03" height="350" width="350">
                          </div>
                          <div class="media-body ml-3">
                            <div class="d-flex align-items-center justify-content-between">
                              <h6 class="mb-0">Kianna Carder</h6>
                              <small class="text-dark"><b>11 : 21 pm</b></small>
                            </div>
                            <small class="mb-0">Lorem ipsum dolor sit amet</small>
                          </div>
                        </div>
                      </a>
                    </div>
                    <a class="right-ic btn btn-primary btn-block position-relative p-2" href="#"
                       role="button">
                      View All
                    </a>
                  </div>
                </div>
              </div>
            </li>
            <li class="nav-item nav-icon dropdown caption-content">
              <a href="#" class="search-toggle dropdown-toggle" id="dropdownMenuButton4"
                 data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                <img ngSrc="assets/images/user/1.png" class="img-fluid rounded" alt="user" height="350" width="350">
              </a>
              <div class="iq-sub-dropdown dropdown-menu" aria-labelledby="dropdownMenuButton">
                <div class="card shadow-none m-0">
                  <div class="card-body p-0 text-center">
                    <div class="media-body profile-detail text-center">
                      <img ngSrc="assets/images/page-img/profile-bg.jpg" alt="profile-bg"
                           class="rounded-top img-fluid mb-4" height="236" width="300">
                      <img ngSrc="assets/images/user/1.png" alt="profile-img"
                           class="rounded profile-img img-fluid avatar-70" height="350" width="350">
                    </div>
                    <div class="p-3">
                      <h5 class="mb-1">therichpost.com</h5>
                      <p class="mb-0">Since 10 march, 2017</p>
                    </div>
                  </div>
                </div>
              </div>
            </li>
          </ul>
        </div>
      </div>
    </nav>
  </div>
</div>
  <!-- Modal Ajouter Employé -->
  <div class="modal fade" id="addEmployeeModal" tabindex="-1" aria-labelledby="addEmployeeModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered modal-lg">
      <div class="modal-content shadow-lg border-0">
        <!-- Header du Modal -->
        <div class="modal-header bg-gradient-primary text-white border-0">
          <h5 class="modal-title fw-bold" id="addEmployeeModalLabel">
            <i class="ri-user-add-line me-2"></i>
            Ajouter un Nouvel Employé
          </h5>
          <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close" (click)="resetForm()"></button>
        </div>

        <!-- Corps du Modal -->
        <div class="modal-body p-4">
          <form #employeeForm="ngForm" (ngSubmit)="onSubmitEmployee()" novalidate>
            <div class="row g-3">
              <!-- Nom -->
              <div class="col-md-6">
                <label for="nom" class="form-label fw-semibold">
                  <i class="ri-user-line me-1 text-primary"></i>Nom *
                </label>
                <input
                  type="text"
                  class="form-control form-control-lg"
                  [class.is-invalid]="formErrors.nom"
                  id="nom"
                  name="nom"
                  [(ngModel)]="newEmployee.nom"
                  placeholder="Entrez le nom"
                  required>
                <div class="invalid-feedback" *ngIf="formErrors.nom">
                  {{ formErrors.nom }}
                </div>
              </div>

              <!-- Prénom -->
              <div class="col-md-6">
                <label for="prenom" class="form-label fw-semibold">
                  <i class="ri-user-line me-1 text-primary"></i>Prénom *
                </label>
                <input
                  type="text"
                  class="form-control form-control-lg"
                  [class.is-invalid]="formErrors.prenom"
                  id="prenom"
                  name="prenom"
                  [(ngModel)]="newEmployee.prenom"
                  placeholder="Entrez le prénom"
                  required>
                <div class="invalid-feedback" *ngIf="formErrors.prenom">
                  {{ formErrors.prenom }}
                </div>
              </div>

              <!-- Email -->
              <div class="col-12">
                <label for="email" class="form-label fw-semibold">
                  <i class="ri-mail-line me-1 text-primary"></i>Adresse Email *
                </label>
                <input
                  type="email"
                  class="form-control form-control-lg"
                  [class.is-invalid]="formErrors.email"
                  id="email"
                  name="email"
                  [(ngModel)]="newEmployee.email"
                  placeholder="<EMAIL>"
                  required>
                <div class="invalid-feedback" *ngIf="formErrors.email">
                  {{ formErrors.email }}
                </div>
              </div>

              <!-- Téléphone -->
              <div class="col-md-6">
                <label for="telephone" class="form-label fw-semibold">
                  <i class="ri-phone-line me-1 text-primary"></i>Téléphone
                </label>
                <input
                  type="tel"
                  class="form-control form-control-lg"
                  id="telephone"
                  name="telephone"
                  [(ngModel)]="newEmployee.telephone"
                  placeholder="+33 1 23 45 67 89">
              </div>

              <!-- Mot de passe -->
              <div class="col-md-6">
                <label for="password" class="form-label fw-semibold">
                  <i class="ri-lock-line me-1 text-primary"></i>Mot de passe *
                </label>
                <input
                  type="password"
                  class="form-control form-control-lg"
                  [class.is-invalid]="formErrors.password"
                  id="password"
                  name="password"
                  [(ngModel)]="newEmployee.password"
                  placeholder="Minimum 6 caractères"
                  required>
                <div class="invalid-feedback" *ngIf="formErrors.password">
                  {{ formErrors.password }}
                </div>
              </div>
            </div>

            <!-- Note informative -->
            <div class="alert alert-info mt-3 border-0 bg-light-info">
              <i class="ri-information-line me-2"></i>
              <small>L'employé recevra un email avec ses identifiants de connexion.</small>
            </div>
          </form>
        </div>

        <!-- Footer du Modal -->
        <div class="modal-footer border-0 bg-light p-4">
          <button type="button" class="btn btn-light btn-lg px-4" data-bs-dismiss="modal" (click)="resetForm()">
            <i class="ri-close-line me-1"></i>Annuler
          </button>
          <button
            type="button"
            class="btn btn-primary btn-lg px-4"
            [disabled]="isSubmitting"
            (click)="onSubmitEmployee()">
            <span *ngIf="isSubmitting" class="spinner-border spinner-border-sm me-2" role="status"></span>
            <i *ngIf="!isSubmitting" class="ri-user-add-line me-1"></i>
            {{ isSubmitting ? 'Ajout en cours...' : 'Ajouter l\'Employé' }}
          </button>
        </div>
      </div>
    </div>
  </div>      <div class="content-page">
  <div class="container-fluid">
    <div class="row">
      <div class="col-lg-4">
        <div class="card card-transparent card-block card-stretch card-height border-none">
          <div class="card-body p-0 mt-lg-2 mt-0">
            <h3 class="mb-3">Hi Graham, Good Morning</h3>
            <p class="mb-0 mr-4">Your dashboard gives you views of key performance or business process.</p>
          </div>
        </div>
      </div>
      <div class="col-lg-8">
        <div class="row">
          <div class="col-lg-4 col-md-4">
            <div class="card card-block card-stretch card-height">
              <div class="card-body">
                <div class="d-flex align-items-center mb-4 card-total-sale">
                  <div class="icon iq-icon-box-2 bg-info-light">
                    <img ngSrc="assets/images/product/1.png" class="img-fluid" alt="image"
                         style="height: auto; width: auto; max-width: 50px; max-height: 50px;">
                  </div>
                  <div>
                    <p class="mb-2">Total Sales</p>
                    <h4>31.50</h4>
                  </div>
                </div>
                <div class="iq-progress-bar mt-2">
                              <span class="bg-info iq-progress progress-1" data-percent="85">
                              </span>
                </div>
              </div>
            </div>
          </div>
          <div class="col-lg-4 col-md-4">
            <div class="card card-block card-stretch card-height">
              <div class="card-body">
                <div class="d-flex align-items-center mb-4 card-total-sale">
                  <div class="icon iq-icon-box-2 bg-danger-light">
                    <img ngSrc="assets/images/product/2.png" class="img-fluid" alt="image"
                         style="height: auto; width: auto; max-width: 50px; max-height: 50px;">
                  </div>
                  <div>
                    <p class="mb-2">Total Cost</p>
                    <h4>$ 4598</h4>
                  </div>
                </div>
                <div class="iq-progress-bar mt-2">
                              <span class="bg-danger iq-progress progress-1" data-percent="70">
                              </span>
                </div>
              </div>
            </div>
          </div>
          <div class="col-lg-4 col-md-4">
            <div class="card card-block card-stretch card-height">
              <div class="card-body">
                <div class="d-flex align-items-center mb-4 card-total-sale">
                  <div class="icon iq-icon-box-2 bg-success-light">
                    <img ngSrc="assets/images/product/3.png" class="img-fluid" alt="image"
                         style="height: auto; width: auto; max-width: 50px; max-height: 50px;">
                  </div>
                  <div>
                    <p class="mb-2">Product Sold</p>
                    <h4>4589 M</h4>
                  </div>
                </div>
                <div class="iq-progress-bar mt-2">
                              <span class="bg-success iq-progress progress-1" data-percent="75">
                              </span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="col-lg-6">
        <div class="card card-block card-stretch card-height">
          <div class="card-header d-flex justify-content-between">
            <div class="header-title">
              <h4 class="card-title">Overview</h4>
            </div>
            <div class="card-header-toolbar d-flex align-items-center">
              <div class="dropdown">
                          <span class="dropdown-toggle dropdown-bg btn" id="dropdownMenuButton001"
                                data-toggle="dropdown">
                              This Month<i class="ri-arrow-down-s-line ml-1"></i>
                          </span>
                <div class="dropdown-menu dropdown-menu-right shadow-none"
                     aria-labelledby="dropdownMenuButton001">
                  <a class="dropdown-item" href="#">Year</a>
                  <a class="dropdown-item" href="#">Month</a>
                  <a class="dropdown-item" href="#">Week</a>
                </div>
              </div>
            </div>
          </div>
          <div class="card-body">
            <div id="layout1-chart1"></div>
          </div>
        </div>
      </div>
      <div class="col-lg-6">
        <div class="card card-block card-stretch card-height">
          <div class="card-header d-flex align-items-center justify-content-between">
            <div class="header-title">
              <h4 class="card-title">Revenue Vs Cost</h4>
            </div>
            <div class="card-header-toolbar d-flex align-items-center">
              <div class="dropdown">
                          <span class="dropdown-toggle dropdown-bg btn" id="dropdownMenuButton002"
                                data-toggle="dropdown">
                              This Month<i class="ri-arrow-down-s-line ml-1"></i>
                          </span>
                <div class="dropdown-menu dropdown-menu-right shadow-none"
                     aria-labelledby="dropdownMenuButton002">
                  <a class="dropdown-item" href="#">Yearly</a>
                  <a class="dropdown-item" href="#">Monthly</a>
                  <a class="dropdown-item" href="#">Weekly</a>
                </div>
              </div>
            </div>
          </div>
          <div class="card-body">
            <div id="layout1-chart-2" style="min-height: 360px;"></div>
          </div>
        </div>
      </div>
      <div class="col-lg-8">
        <div class="card card-block card-stretch card-height">
          <div class="card-header d-flex align-items-center justify-content-between">
            <div class="header-title">
              <h4 class="card-title">Top Products</h4>
            </div>
            <div class="card-header-toolbar d-flex align-items-center">
              <div class="dropdown">
                          <span class="dropdown-toggle dropdown-bg btn" id="dropdownMenuButton006"
                                data-toggle="dropdown">
                              This Month<i class="ri-arrow-down-s-line ml-1"></i>
                          </span>
                <div class="dropdown-menu dropdown-menu-right shadow-none"
                     aria-labelledby="dropdownMenuButton006">
                  <a class="dropdown-item" href="#">Year</a>
                  <a class="dropdown-item" href="#">Month</a>
                  <a class="dropdown-item" href="#">Week</a>
                </div>
              </div>
            </div>
          </div>
          <div class="card-body">
            <ul class="list-unstyled row top-product mb-0">
              <li class="col-lg-3">
                <div class="card card-block card-stretch card-height mb-0">
                  <div class="card-body">
                    <div class="bg-warning-light rounded">
                      <img ngSrc="assets/images/product/01.png" class="style-img img-fluid m-auto p-3" alt="image"
                           height="160" width="160">
                    </div>
                    <div class="style-text text-left mt-3">
                      <h5 class="mb-1">Organic Cream</h5>
                      <p class="mb-0">789 Item</p>
                    </div>
                  </div>
                </div>
              </li>
              <li class="col-lg-3">
                <div class="card card-block card-stretch card-height mb-0">
                  <div class="card-body">
                    <div class="bg-danger-light rounded">
                      <img ngSrc="assets/images/product/02.png" class="style-img img-fluid m-auto p-3" alt="image"
                           height="160" width="160">
                    </div>
                    <div class="style-text text-left mt-3">
                      <h5 class="mb-1">Rain Umbrella</h5>
                      <p class="mb-0">657 Item</p>
                    </div>
                  </div>
                </div>
              </li>
              <li class="col-lg-3">
                <div class="card card-block card-stretch card-height mb-0">
                  <div class="card-body">
                    <div class="bg-info-light rounded">
                      <img ngSrc="assets/images/product/03.png" class="style-img img-fluid m-auto p-3" alt="image"
                           height="160" width="160">
                    </div>
                    <div class="style-text text-left mt-3">
                      <h5 class="mb-1">Serum Bottle</h5>
                      <p class="mb-0">489 Item</p>
                    </div>
                  </div>
                </div>
              </li>
              <li class="col-lg-3">
                <div class="card card-block card-stretch card-height mb-0">
                  <div class="card-body">
                    <div class="bg-success-light rounded">
                      <img ngSrc="assets/images/product/02.png" class="style-img img-fluid m-auto p-3" alt="image"
                           height="160" width="160">
                    </div>
                    <div class="style-text text-left mt-3">
                      <h5 class="mb-1">Organic Cream</h5>
                      <p class="mb-0">468 Item</p>
                    </div>
                  </div>
                </div>
              </li>
            </ul>
          </div>
        </div>
      </div>
      <div class="col-lg-4">
        <div class="card card-transparent card-block card-stretch mb-4">
          <div class="card-header d-flex align-items-center justify-content-between p-0">
            <div class="header-title">
              <h4 class="card-title mb-0">Best Item All Time</h4>
            </div>
            <div class="card-header-toolbar d-flex align-items-center">
              <div><a href="#" class="btn btn-primary view-btn font-size-14">View All</a></div>
            </div>
          </div>
        </div>
        <div class="card card-block card-stretch card-height-helf">
          <div class="card-body card-item-right">
            <div class="d-flex align-items-top">
              <div class="bg-warning-light rounded">
                <img ngSrc="assets/images/product/04.png" class="style-img img-fluid m-auto" alt="image" height="100"
                     width="100">
              </div>
              <div class="style-text text-left">
                <h5 class="mb-2">Coffee Beans Packet</h5>
                <p class="mb-2">Total Sell : 45897</p>
                <p class="mb-0">Total Earned : $45,89 M</p>
              </div>
            </div>
          </div>
        </div>
        <div class="card card-block card-stretch card-height-helf">
          <div class="card-body card-item-right">
            <div class="d-flex align-items-top">
              <div class="bg-danger-light rounded">
                <img ngSrc="assets/images/product/05.png" class="style-img img-fluid m-auto" alt="image" height="100"
                     width="100">
              </div>
              <div class="style-text text-left">
                <h5 class="mb-2">Bottle Cup Set</h5>
                <p class="mb-2">Total Sell : 44359</p>
                <p class="mb-0">Total Earned : $45,50 M</p>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="col-lg-4">
        <div class="card card-block card-stretch card-height-helf">
          <div class="card-body">
            <div class="d-flex align-items-top justify-content-between">
              <div class="">
                <p class="mb-0">Income</p>
                <h5>$ 98,7800 K</h5>
              </div>
              <div class="card-header-toolbar d-flex align-items-center">
                <div class="dropdown">
                              <span class="dropdown-toggle dropdown-bg btn" id="dropdownMenuButton003"
                                    data-toggle="dropdown">
                                  This Month<i class="ri-arrow-down-s-line ml-1"></i>
                              </span>
                  <div class="dropdown-menu dropdown-menu-right shadow-none"
                       aria-labelledby="dropdownMenuButton003">
                    <a class="dropdown-item" href="#">Year</a>
                    <a class="dropdown-item" href="#">Month</a>
                    <a class="dropdown-item" href="#">Week</a>
                  </div>
                </div>
              </div>
            </div>
            <div id="layout1-chart-3" class="layout-chart-1"></div>
          </div>
        </div>
        <div class="card card-block card-stretch card-height-helf">
          <div class="card-body">
            <div class="d-flex align-items-top justify-content-between">
              <div class="">
                <p class="mb-0">Expenses</p>
                <h5>$ 45,8956 K</h5>
              </div>
              <div class="card-header-toolbar d-flex align-items-center">
                <div class="dropdown">
                              <span class="dropdown-toggle dropdown-bg btn" id="dropdownMenuButton004"
                                    data-toggle="dropdown">
                                  This Month<i class="ri-arrow-down-s-line ml-1"></i>
                              </span>
                  <div class="dropdown-menu dropdown-menu-right shadow-none"
                       aria-labelledby="dropdownMenuButton004">
                    <a class="dropdown-item" href="#">Year</a>
                    <a class="dropdown-item" href="#">Month</a>
                    <a class="dropdown-item" href="#">Week</a>
                  </div>
                </div>
              </div>
            </div>
            <div id="layout1-chart-4" class="layout-chart-2"></div>
          </div>
        </div>
      </div>
      <div class="col-lg-8">
        <div class="card card-block card-stretch card-height">
          <div class="card-header d-flex justify-content-between">
            <div class="header-title">
              <h4 class="card-title">Order Summary</h4>
            </div>
            <div class="card-header-toolbar d-flex align-items-center">
              <div class="dropdown">
                          <span class="dropdown-toggle dropdown-bg btn" id="dropdownMenuButton005"
                                data-toggle="dropdown">
                              This Month<i class="ri-arrow-down-s-line ml-1"></i>
                          </span>
                <div class="dropdown-menu dropdown-menu-right shadow-none"
                     aria-labelledby="dropdownMenuButton005">
                  <a class="dropdown-item" href="#">Year</a>
                  <a class="dropdown-item" href="#">Month</a>
                  <a class="dropdown-item" href="#">Week</a>
                </div>
              </div>
            </div>
          </div>
          <div class="card-body">
            <div class="d-flex flex-wrap align-items-center mt-2">
              <div class="d-flex align-items-center progress-order-left">
                <div class="progress progress-round m-0 orange conversation-bar" data-percent="46">
                              <span class="progress-left">
                                  <span class="progress-bar"></span>
                              </span>
                  <span class="progress-right">
                                  <span class="progress-bar"></span>
                              </span>
                  <div class="progress-value text-secondary">46%</div>
                </div>
                <div class="progress-value ml-3 pr-5 border-right">
                  <h5>$12,6598</h5>
                  <p class="mb-0">Average Orders</p>
                </div>
              </div>
              <div class="d-flex align-items-center ml-5 progress-order-right">
                <div class="progress progress-round m-0 primary conversation-bar" data-percent="46">
                              <span class="progress-left">
                                  <span class="progress-bar"></span>
                              </span>
                  <span class="progress-right">
                                  <span class="progress-bar"></span>
                              </span>
                  <div class="progress-value text-primary">46%</div>
                </div>
                <div class="progress-value ml-3">
                  <h5>$59,8478</h5>
                  <p class="mb-0">Top Orders</p>
                </div>
              </div>
            </div>
          </div>
          <div class="card-body pt-0">
            <div id="layout1-chart-5"></div>
          </div>
        </div>
      </div>
    </div>
    <!-- Page end  -->
  </div>
</div>
</div>
<!-- Wrapper End-->
<footer class="iq-footer">
  <div class="container-fluid">
    <div class="card">
      <div class="card-body">
        <div class="row">
          <div class="col-lg-6">
          </div>
        </div>
      </div>
    </div>
  </div>
</footer>
