import { Component, CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import {NgOptimizedImage, CommonModule} from '@angular/common';
import {FormsModule} from '@angular/forms';
import {CreateUserRequest, UserControllerService} from '../../api';
import {Router} from '@angular/router';
import {ToastService} from '../../services/toast.service';

@Component({
  selector: 'app-dashboard',
  standalone: true,
  imports: [NgOptimizedImage, FormsModule, CommonModule],
  templateUrl: './dashboard.html',
  styleUrl: './dashboard.css',
  schemas: [CUSTOM_ELEMENTS_SCHEMA]
})
export class Dashboard {
  // Modèle pour le formulaire d'ajout d'employé
  newEmployee: CreateUserRequest = {
    nom: '',
    prenom: '',
    email: '',
    password: '',
    telephone: ''
  };

  // État du formulaire
  isSubmitting = false;
  formErrors: any = {};

  constructor(
    private userService: UserControllerService,
    private router: Router,
    private toastService: ToastService
  ) {}

  // Réinitialiser le formulaire
  resetForm() {
    this.newEmployee = {
      nom: '',
      prenom: '',
      email: '',
      password: '',
      telephone: ''
    };
    this.formErrors = {};
    this.isSubmitting = false;
  }

  // Valider le formulaire
  validateForm(): boolean {
    this.formErrors = {};
    let isValid = true;

    if (!this.newEmployee.nom?.trim()) {
      this.formErrors.nom = 'Le nom est requis';
      isValid = false;
    }

    if (!this.newEmployee.prenom?.trim()) {
      this.formErrors.prenom = 'Le prénom est requis';
      isValid = false;
    }

    if (!this.newEmployee.email?.trim()) {
      this.formErrors.email = 'L\'email est requis';
      isValid = false;
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(this.newEmployee.email)) {
      this.formErrors.email = 'Format d\'email invalide';
      isValid = false;
    }

    if (!this.newEmployee.password?.trim()) {
      this.formErrors.password = 'Le mot de passe est requis';
      isValid = false;
    } else if (this.newEmployee.password.length < 6) {
      this.formErrors.password = 'Le mot de passe doit contenir au moins 6 caractères';
      isValid = false;
    }

    return isValid;
  }

  // Soumettre le formulaire
  onSubmitEmployee() {
    if (!this.validateForm()) {
      return;
    }

    this.isSubmitting = true;

    this.userService.createUser(this.newEmployee).subscribe({
      next: (response) => {
        // Succès
        this.showSuccessMessage('Employé ajouté avec succès !');
        this.resetForm();
        this.closeModal();
      },
      error: (error) => {
        console.error('Erreur lors de l\'ajout:', error);
        this.showErrorMessage('Erreur lors de l\'ajout de l\'employé');
        this.isSubmitting = false;
      }
    });
  }

  // Fermer le modal
  closeModal() {
    const modal = document.getElementById('addEmployeeModal');
    if (modal) {
      // Utiliser Bootstrap pour fermer le modal
      const bootstrapModal = (window as any).bootstrap?.Modal?.getInstance(modal);
      if (bootstrapModal) {
        bootstrapModal.hide();
      }
    }
    this.resetForm();
  }

  // Afficher message de succès
  private showSuccessMessage(message: string) {
    this.toastService.showSuccess('Succès', message);
  }

  // Afficher message d'erreur
  private showErrorMessage(message: string) {
    this.toastService.showError('Erreur', message);
  }
}
