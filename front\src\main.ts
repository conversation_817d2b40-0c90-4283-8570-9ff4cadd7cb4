import { bootstrapApplication } from '@angular/platform-browser';
import { importProvidersFrom, APP_INITIALIZER } from '@angular/core';
import { App } from './app/app';
import { appConfig } from './app/app.config';

import { KeycloakAngularModule, KeycloakService } from 'keycloak-angular';
import keycloakConfig from './app/guards/keycloak.config'; // Crée ce fichier

// Fonction d'initialisation de Keycloak
function initializeKeycloak(keycloak: KeycloakService) {
  return () => keycloak.init(keycloakConfig);
}

bootstrapApplication(App, {
  ...appConfig,
  providers: [
    ...appConfig.providers || [],
    importProvidersFrom(KeycloakAngularModule),
    {
      provide: APP_INITIALIZER,
      useFactory: initializeKeycloak,
      multi: true,
      deps: [KeycloakService]
    }
  ]
}).catch(err => console.error(err));
