import { bootstrapApplication } from '@angular/platform-browser';
import { importProvidersFrom, APP_INITIALIZER } from '@angular/core';
import { App } from './app/app';
import { appConfig } from './app/app.config';

import { KeycloakAngularModule, KeycloakService } from 'keycloak-angular';
import keycloakConfig from './app/guards/keycloak.config'; // Crée ce fichier

// Fonction d'initialisation de Keycloak
function initializeKeycloak(keycloak: KeycloakService) {
  return () => {
    console.log('Initialisation de Keycloak...', keycloakConfig);
    return keycloak.init(keycloakConfig)
      .then((authenticated) => {
        console.log('Keycloak initialisé. Utilisateur authentifié:', authenticated);
        if (authenticated) {
          console.log('Utilisateur:', keycloak.getUsername());
          console.log('Rôles:', keycloak.getUserRoles());
        }
      })
      .catch((error) => {
        console.error('Erreur lors de l\'initialisation de Keycloak:', error);
      });
  };
}

bootstrapApplication(App, {
  ...appConfig,
  providers: [
    ...appConfig.providers || [],
    importProvidersFrom(KeycloakAngularModule),
    {
      provide: APP_INITIALIZER,
      useFactory: initializeKeycloak,
      multi: true,
      deps: [KeycloakService]
    }
  ]
}).catch(err => console.error(err));
